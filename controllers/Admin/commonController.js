const bcrypt = require("bcrypt");
const commonModel = require("../../models/Admin/commonModel");
const commonFunction = require("../../assets/common");
const staffModel = require("../../models/Admin/staffModel");
const companyModel = require("../../models/Admin/companyModel");
const axios = require("axios");
const ActionLogModel = require("../../models/Admin/ActionLogModel");

exports.getLogsListController = async (request, response) => {
	try {
		console.log("request", request.company_id)
		request.query.search = request.query.search ? request.query.search : "";
		const responseData = await ActionLogModel.fetchActionLog(request.company_id,request)
		response.status(200).json({
			status: 1,
			message: "success",
			data: responseData,
		});
	} catch (error) {
		console.log("exports.getLogsListController -> error: ", error);
		response.status(500).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
}

exports.getPlaceDetailsController = async (request, response) => {
	try {
		const placeId = request.body.placeId;
		const apiResponse = await axios.get(`https://maps.googleapis.com/maps/api/place/details/json?placeid=${placeId}&key=${GOOGLE_API_KEY}&libraries=places`);
		const responseData = apiResponse.data;

		response.status(200).json({
			status: 1,
			message: "success",
			data: responseData,
		});
	} catch (error) {
		console.log("exports.getPlaceDetailsController -> error: ", error);
		response.status(500).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
}

exports.superAdminCompanyUpdatePassword = async (request, response) => {
	try {

		let { company_id, new_password } = request.body;
		let findCompany = await companyModel.superAdminCompanyUpdatePasswordFindCompany(company_id);
		let pwdChanged = await companyModel.superAdminCompanyUpdatePassword({
			company_id,
			new_password,
		});

		const findStaffDetailsEmail = await commonModel.findStaffDetailsEmail(findCompany.email);

		if (findStaffDetailsEmail) {
			let pwdChanged = await companyModel.superAdminStaffUpdatePassword(
				findStaffDetailsEmail.staff_id,
				new_password,
			);
		}


		if (Array.isArray(pwdChanged) && pwdChanged[0] === 1) {
			let html = await commonFunction.readFile("welcome.html");
			html = html.replace(/{{name}}/g, findCompany.company_name);
			html = html.replace(/{{email}}/g, findCompany.email);
			html = html.replace(/{{password}}/g, new_password);
			html = html.replace(/{{cmsUrl}}/g, CMS_URL_LINK);

			await commonFunction.sendEmail(
				findCompany.email,
				"Password Update",
				html
			);

			response.status(SUCCESS_CODE).json({
				status: 1,
				message: PASSWORD_RESET_SUCCESS,
				data: {},
			});

		} else {
			response.status(SUCCESS_CODE).json({
				status: 0,
				message: PASSWORD_RESET_ERROR,
				data: {},
			});
		}
	}
	catch (error) {
		console.log("exports.superAdminCompanyUpdatePassword -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
}

exports.cmsResetPassword = async (request, response) => {
	try {
		let isStaff = await staffModel.verifyToken(request.body.code);
		let isCompany = await companyModel.verifyToken(request.body.code);
		let isAdmin = await commonModel.cmsverifyToken(request.body.code);
		if (isStaff === 1) {
			let { code, new_password } = request.body;
			let pwdChanged = await staffModel.resetPassword({
				code,
				new_password,
			});
			if (Array.isArray(pwdChanged) && pwdChanged[0] === 1) {
				response.status(SUCCESS_CODE).json({
					status: 1,
					message: PASSWORD_RESET_SUCCESS,
					data: {},
				});
			} else {
				response.status(SUCCESS_CODE).json({
					status: 0,
					message: PASSWORD_RESET_ERROR,
					data: {},
				});
			}
		}
		else if (isCompany === 1) {
			let { code, new_password } = request.body;
			let pwdChanged = await companyModel.resetPassword({
				code,
				new_password,
			});
			if (Array.isArray(pwdChanged) && pwdChanged[0] === 1) {
				response.status(SUCCESS_CODE).json({
					status: 1,
					message: PASSWORD_RESET_SUCCESS,
					data: {},
				});
			} else {
				response.status(SUCCESS_CODE).json({
					status: 0,
					message: PASSWORD_RESET_ERROR,
					data: {},
				});
			}
		}
		else if (isAdmin === 1) {
			let { code, new_password } = request.body;
			let pwdChanged = await commonModel.CMSResetPassword({
				code,
				new_password,
			});
			if (Array.isArray(pwdChanged) && pwdChanged[0] === 1) {
				response.status(SUCCESS_CODE).json({
					status: 1,
					message: PASSWORD_RESET_SUCCESS,
					data: {},
				});
			} else {
				response.status(SUCCESS_CODE).json({
					status: 0,
					message: PASSWORD_RESET_ERROR,
					data: {},
				});
			}
		}
		else {
			response.status(SUCCESS_CODE).json({
				status: 0,
				message: TOKEN_VERIFICATION_ERROR,
				data: {},
			});
		}

	}
	catch (error) {
		console.log("exports.resetPassword -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
}


exports.notificationStatusChange = async (request, response) => {
	try {
		let getUserDetails = await commonModel.getUserDetails(request);
		const notificationStatusChange = await commonModel.notificationStatusChange(getUserDetails)
		if (notificationStatusChange)
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				1,
				"Success",
				{}
			);
		else
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				0,
				"Fail",
				{}
			);
	}
	catch (error) {
		console.log("exports.notificationStatusChange -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
}

exports.adminSignInWithCompanyId = async (request, response) => {
	try {
		const signInData = await commonModel.adminSignInWithCompanyId(request.body)
		const tokenDetail = await commonModel.accesstokenInsert(
			signInData.staff_id ? null : signInData.company_id,
			signInData.admin_id,
			signInData.staff_id,
			request.body,
			signInData
		);
		signInData["access_token"] = tokenDetail["access_token"];
		signInData.photo !== "" && signInData.photo !== null
			? signInData.photo.includes("http")
				? (signInData["user_profile_pic"] = signInData.photo)
				: (signInData["user_profile_pic"] =
					Const_AWS_BASE_Staff_Profile + "original/" + signInData.photo)
			: "";

		ActionLogModel.createActionLog(
			{
				platform: "CMS",
				performed_by_id: signInData.admin_id ? signInData.admin_id : signInData.staff_id ? signInData.staff_id : signInData.company_id,
				performed_by_role: signInData.admin_id ? "Admin" : signInData.staff_id ? "User" : "Company",
				performed_by_name: signInData.admin_id ? `${signInData.first_name} ${signInData.last_name}` : signInData.staff_id ? `${signInData.first_name} ${signInData.last_name}` : signInData.company_name,
				action_type: "LOGIN",
				company_id: signInData.company_id
			}
		)

		response.status(SUCCESS_CODE).json({
			status: 1,
			message: LOGIN_SUCCESS,
			data: signInData.admin_id
				? { ...signInData, userType: 1 }
				: { ...signInData, userType: 2 },
		});
	}
	catch (error) {
		console.log("exports.adminSignInWithCompanyId -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
}


exports.signIn = async (request, response) => {
	try {
		const signInData = await commonModel.signIn(request.body);
		if (signInData && signInData !== "" && signInData !== null) {
			if (signInData.roles === "WORKER") {
				response.status(403).json({
					status: 0,
					message: "Worker are not allowed to login in cms!",
					data: {},
				});
			}
			else if (
				signInData.status === "Inactive" ||
				signInData.status === "inactive" ||
				signInData.status === "INACTIVE"
			) {
				response.status(403).json({
					status: 0,
					message: USER_INACTIVE,
					data: {},
				});
			}
			else {
				//password check
				let passwordVerified = bcrypt.compareSync(
					request.body.password,
					signInData.password
				);
				if (passwordVerified === false) {
					response.status(EXPECTATION_FAILED_CODE).json({
						status: 0,
						message: LOGIN_PASSWORD_FAIL,
						data: {},
					});
				}
				else {
					try {
						const tokenDetail = await commonModel.accesstokenInsert(
							signInData.staff_id ? null : signInData.company_id,
							signInData.admin_id,
							signInData.staff_id,
							request.body,
							signInData
						);
						signInData["access_token"] = tokenDetail["access_token"];
						signInData.photo !== "" && signInData.photo !== null
							? signInData.photo.includes("http")
								? (signInData["user_profile_pic"] = signInData.photo)
								: (signInData["user_profile_pic"] =
									Const_AWS_BASE_Staff_Profile + "original/" + signInData.photo)
							: "";
						response.status(SUCCESS_CODE).json({
							status: 1,
							message: LOGIN_SUCCESS,
							data: signInData.admin_id
								? { ...signInData, userType: 1 }
								: { ...signInData, userType: 2 },
						});

						ActionLogModel.createActionLog(
							{
								platform: "CMS",
								performed_by_id: signInData.admin_id ? signInData.admin_id : signInData.staff_id ? signInData.staff_id : signInData.company_id,
								performed_by_role: signInData.admin_id ? "Admin" : signInData.staff_id ? "User" : "Company",
								performed_by_name: signInData.admin_id ? `${signInData.first_name} ${signInData.last_name}` : signInData.staff_id ? `${signInData.first_name} ${signInData.last_name}` : signInData.company_name,
								action_type: "LOGIN",
							}
						)

					} catch (error) {
						response.status(EXPECTATION_FAILED_CODE).json({
							status: 0,
							message: LOGIN_FAIL,
							data: {},
						});
					}
				}
			}
		}
		else {
			response.status(EXPECTATION_FAILED_CODE).json({
				status: 0,
				message: LOGIN_EMAIL_FAIL,
				data: {},
			});
		}

	} catch (error) {
		console.log("exports.signIn -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
};

exports.adminChangePassword = async (request, response) => {
	try {
		const signInData = await commonModel.signIn(request.body);
		if (signInData && signInData !== "" && signInData !== null) {
			if (signInData.roles === "WORKER") {
				response.status(403).json({
					status: 0,
					message: "Worker are not allowed to login in cms!",
					data: {},
				});
			}
			else if (
				signInData.status === "Inactive" ||
				signInData.status === "inactive" ||
				signInData.status === "INACTIVE"
			) {
				response.status(403).json({
					status: 0,
					message: USER_INACTIVE,
					data: {},
				});
			}
			else {
				//password check
				let passwordVerified = bcrypt.compareSync(
					request.body.old_password,
					signInData.password
				);
				if (!passwordVerified) {
					response.status(EXPECTATION_FAILED_CODE).json({
						status: 0,
						message: "Old password is wrong",
						data: {},
					});
				}
				else {
					let headerData = await commonFunction.jwtTokenDecode(
						request.headers.access_token
					);
					const editPassword = await commonModel.adminEditPassword(
						request,
						headerData.payload.user_id,
						signInData
					);
					if (editPassword.includes(1)) {
						response.status(SUCCESS_CODE).json({
							status: 1,
							message: CHANGE_PASSWORD_SUCCESS,
							data: {},
						});
					}
					else {
						response.status(EXPECTATION_FAILED_CODE).json({
							status: 0,
							message: CHANGE_PASSWORD_FAIL,
							data: {},
						});
					}
				}
			}
		}
		else {
			response.status(EXPECTATION_FAILED_CODE).json({
				status: 0,
				message: "Email not found",
				data: {},
			});
		}
	}
	catch (error) {
		console.log("exports.changePassword -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
}

exports.adminForgotPassword = async (request, response) => {
	try {
		const signInData = await commonModel.signIn(request.body);
		const data = signInData;

		if (signInData && signInData !== "" && signInData !== null) {
			if (signInData.roles === "WORKER") {
				response.status(403).json({
					status: 0,
					message: "Worker are not allowed to login in cms!",
					data: {},
				});
			}
			else if (
				signInData.status === "Inactive" ||
				signInData.status === "inactive" ||
				signInData.status === "INACTIVE"
			) {
				response.status(403).json({
					status: 0,
					message: USER_INACTIVE,
					data: {},
				});
			}
			else {
				let verificationToken = await commonModel.updateVerificationToken(
					request.body.email,
					data
				);

				const adminDetail = await commonModel.signIn(request.body);
				let buff = Buffer.from("'" + verificationToken + "'");
				let encToken = buff.toString("base64");
				let html = await commonFunction.readFile("ForgotPasswordApp.html");
				html = html.replace(/{{action_link}}/g, RESET_PASSWORD_LINK + encToken);
				html = html.replace(/{{name}}/g, adminDetail.first_name ? adminDetail.first_name : adminDetail.company_name);
				html = html.replace(/{{otp}}/g, verificationToken);
				let isEmailSent = await commonFunction.sendEmail(
					request.body.email,
					"Reset Password",
					html
				);
				if (isEmailSent == false) {
					throw Error("Unable to send email");
				}
				response.status(SUCCESS_CODE).json({
					status: 1,
					message: FORGOT_EMAIL_SENT,
					data: data,
				});
			}
		}
		else {
			response.status(EXPECTATION_FAILED_CODE).json({
				status: 0,
				message: "Email not found",
				data: {},
			});
		}
	}
	catch (error) {
		console.log("exports.forgotPassword -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
}

exports.adminResetPassword = async (request, response) => {
	try {
		let isVerified = await commonModel.verifyToken(request.body);
		if (isVerified === 1) {
			let { code,
				new_password,
				adminId,
				companyId,
				staffId } = request.body;
			let pwdChanged = await commonModel.resetPassword({
				code,
				new_password,
				adminId,
				companyId,
				staffId
			});
			if (Array.isArray(pwdChanged) && pwdChanged[0] === 1) {
				response.status(SUCCESS_CODE).json({
					status: 1,
					message: PASSWORD_RESET_SUCCESS,
					data: {},
				});
			} else {
				response.status(SUCCESS_CODE).json({
					status: 0,
					message: PASSWORD_RESET_ERROR,
					data: {},
				});
			}
		} else {
			response.status(SUCCESS_CODE).json({
				status: 0,
				message: TOKEN_VERIFICATION_ERROR,
				data: {},
			});
		}
	}
	catch (error) {
		console.log("exports.resetPassword -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
}

exports.dashboard = async (request, response) => {
	try {
		const { company_id } = request.body;
		const data = await commonModel.countForDashboard(company_id, request.body);
		if (data !== "" && data !== null && data !== undefined) {
			response.status(SUCCESS_CODE).json({
				status: 1,
				message: "Data retrieved!",
				data: data,
			});
		}
		else {
			response.status(SERVER_ERROR_CODE).json({
				status: 0,
				message: "Data not found!",
				data: {},
			});
		}
	} catch (error) {
		console.log("exports.dashboard -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
};

exports.lisExternalApiUsers = async (request, response) => {
	try {
		const data = await commonModel.lisExternalApiUsers();
		if (data) {
			response.status(SUCCESS_CODE).json({
				status: 1,
				message: "Fetched",
				data: data,
			});
		}
		else {
			response.status(SERVER_ERROR_CODE).json({
				status: 0,
				message: "Data not found!",
				data: {},
			});
		}
	} catch (error) {
		console.log("exports.lisExternalApiUsers -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
};
exports.addExternalApiUser = async (request, response) => {
	try {
		const body = request.body;
		const adminEmail = await commonModel.findAdminEmail(body.email);
		const companyEmail = await commonModel.findCompanyEmail(body.email);
		const staffEmail = await commonModel.findStaffEmail(body.email);
		const customerEmail = await commonModel.findCustomerEmail(body.email);
		if (adminEmail || companyEmail || staffEmail || customerEmail) {
			response.status(SERVER_ERROR_CODE).json({
				status: 0,
				message: "Email already exists!",
				data: {},
			});
		}
		else {
			const data = await commonModel.addExternalApiUser(body);
			if (data !== "" && data !== null && data !== undefined) {
				response.status(SUCCESS_CODE).json({
					status: 1,
					message: "API User added!",
					data: data,
				});
			} else {
				response.status(SERVER_ERROR_CODE).json({
					status: 0,
					message: "User add error!",
					data: {},
				});
			}
		}
	} catch (error) {
		console.log("exports.addExternalApiUser -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
};

exports.reloadToken = async (request, response) => {
	try {
		const body = request.body;
		const data = await commonModel.findApiUser(body.email);
		if (data !== "" && data !== null && data !== undefined) {
			let passwordVerified = bcrypt.compareSync(body.password, data.password);
			if (!passwordVerified) {
				response.status(EXPECTATION_FAILED_CODE).json({
					status: 0,
					message: LOGIN_PASSWORD_FAIL,
					data: {},
				});
			}
			else {
				const data = await commonModel.changeUserToken(body.email);
				if (data) {
					const token = await commonModel.findToken(body.email);
					response.status(SUCCESS_CODE).json({
						status: 1,
						message: "API token changed!",
						data: { apiKey: token },
					});
				}
			}
		} else {
			response.status(BAD_REQUEST_CODE).json({
				status: 0,
				message: "API user not found!",
				data: {},
			});
		}
	} catch (error) {
		console.log("exports.reloadToken -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
};
