"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.addColumn(
        'app_logs',
        'company_id',
        {
          type: Sequelize.INTEGER,
          allowNull: true,
          onDelete: "CASCADE",
          onUpdate: "CASCADE",
          references: {
            model: "companies",
            key: "company_id",
          }
        },
      );

      await queryInterface.addColumn(
        'app_logs',
        'shipment_job_id',
        {
          type: Sequelize.INTEGER,
          allowNull: true,
          onDelete: "CASCADE",
          onUpdate: "CASCADE",
          references: {
            model: "shipment_jobs",
            key: "shipment_job_id",
          },
        },
      );

      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.removeColumn("app_logs", "company_id");
      await queryInterface.removeColumn("app_logs", "shipment_job_id");
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};