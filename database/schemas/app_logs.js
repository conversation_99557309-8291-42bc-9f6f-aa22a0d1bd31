"use strict";
module.exports = (sequelize, DataTypes) => {
	const app_logs = sequelize.define(
		"app_logs",
		{
			log_id: {
				type: DataTypes.INTEGER,
				primaryKey: true,
				autoIncrement: true,
			},
			platform: {
				type: DataTypes.ENUM("APP", "CMS"),
				allowNull: false,
				comment: "Platform where action occurred - APP or CMS"
			},
			performed_by_id: {
				type: DataTypes.INTEGER,
				allowNull: true,
			},
			performed_by_role: {
				type: DataTypes.ENUM('Admin', 'Company', 'User'),
				allowNull: true,
			},
			performed_by_name: {
				type: DataTypes.STRING,
				allowNull: true,
			},
			action_performed_on_id: {
				type: DataTypes.INTEGER,
				allowNull: true,
			},
			action_performed_on_name: {
				type: DataTypes.STRING,
				allowNull: true,
			},
			action_type: {
				type: DataTypes.ENUM(
					"LOGIN",
					"LOGOUT",

					"STAGE_ASSIGN_TO_USER",
					"STAGE_REMOVE_FROM_USER",

					"ITEM_CREATE",
					"ITEM_UPDATE",
					"ITEM_DELETE",
					"ITEM_ADDED_COUNT",

					"ITEM_ASSIGN_TO_STORAGE",
					"ITEM_REMOVE_FROM_STORAGE",
					"ITEM_REMOVE_FROM_INVENTORY",

					"SHIPMENT_CREATE",
					"SHIPMENT_UPDATE",
					"SHIPMENT_DELETE",
					"SHIPMENT_STAGE_COMPLETED",

					"ITEM_SUGGESTION_CREATE",
					"ITEM_SUGGESTION_UPDATE",
					"ITEM_SUGGESTION_DELETE",
					"ITEM_SUGGESTION_ACTIVATE",
					"ITEM_SUGGESTION_DEACTIVATE",

					"ROOM_CREATE",
					"ROOM_UPDATE",
					"ROOM_DELETE",
					"ROOM_ACTIVATE",
					"ROOM_DEACTIVATE",

					"SHIPMENT_TYPE_CREATE",
					"SHIPMENT_TYPE_UPDATE",
					"SHIPMENT_TYPE_DELETE",
					"SHIPMENT_TYPE_ACTIVATE",
					"SHIPMENT_TYPE_DEACTIVATE",

					"SHIPMENT_TYPE_STAGE_UPDATE",
					"SHIPMENT_TYPE_STAGE_ACTIVATE",
					"SHIPMENT_TYPE_STAGE_DEACTIVATE",

					"SHIPMENT_TYPE_STAGE_OF_SHIPMENT_CREATE",
					"SHIPMENT_TYPE_STAGE_OF_SHIPMENT_UPDATE",
					"SHIPMENT_TYPE_STAGE_OF_SHIPMENT_DELETE",

					"TAG_CREATE",
					"TAG_UPDATE",
					"TAG_DELETE",

					"USER_CREATE",
					"USER_UPDATE",
					"USER_DELETE",
					"USER_ACTIVATE",
					"USER_DEACTIVATE",

					"CUSTOMER_CREATE",
					"CUSTOMER_UPDATE",
					"CUSTOMER_DELETE",
					"CUSTOMER_ACTIVATE",
					"CUSTOMER_DEACTIVATE",

					"VIEW_ITEMS",
					"SCAN_QR",
					"MANUAL_SELECT"
				),
				allowNull: false,
				comment: "Type of action performed"
			},
			company_id: {
				type: DataTypes.INTEGER,
				allowNull: true,
			},
			shipment_job_id: {
				type: DataTypes.INTEGER,
				allowNull: true,
			},
			affected_fields: {
				type: DataTypes.JSON,
				allowNull: true,
			},
			created_at: {
				type: DataTypes.DATE,
				allowNull: false,
				defaultValue: DataTypes.NOW,
				comment: "Timestamp when action occurred"
			},
			updated_at: {
				type: DataTypes.DATE,
				allowNull: false,
				defaultValue: DataTypes.NOW,
				comment: "Timestamp when log was last updated"
			}
		},
		{
			createdAt: false,
			updatedAt: false,
		}
	);
	return app_logs;
};
